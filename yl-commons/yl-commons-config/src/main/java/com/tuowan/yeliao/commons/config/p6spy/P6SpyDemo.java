package com.tuowan.yeliao.commons.config.p6spy;

/**
 * P6Spy日志格式演示
 * 展示新的日志格式和颜色效果
 */
public class P6SpyDemo {

    public static void main(String[] args) {
        System.out.println("=== P6Spy 新日志格式演示 ===\n");
        
        // 设置不同环境进行演示
        demonstrateFormat("dev", "********************************************************");
        demonstrateFormat("test", "******************************************************");
        demonstrateFormat("prod", "******************************************************");
    }

    private static void demonstrateFormat(String env, String url) {
        System.out.println("--- 环境: " + env.toUpperCase() + " ---");
        
        // 设置环境
        System.setProperty("spring.profiles.active", env);
        
        // 创建实例
        P6SpyLogger logger = new P6SpyLogger();
        StdoutLogger stdoutLogger = new StdoutLogger();
        
        // 演示不同类型的SQL
        String[] sqls = {
            "SELECT * FROM t_settings WHERE setting_key = 'app.version'",
            "INSERT INTO t_user (username, email, create_time) VALUES ('testuser', '<EMAIL>', NOW())",
            "UPDATE t_user SET last_login_time = NOW() WHERE user_id = 12345",
            "DELETE FROM t_temp_data WHERE create_time < DATE_SUB(NOW(), INTERVAL 7 DAY)",
            "SELECT u.username, p.title FROM t_user u LEFT JOIN t_post p ON u.user_id = p.user_id WHERE u.status = 1 ORDER BY u.create_time DESC LIMIT 10"
        };
        
        for (int i = 0; i < sqls.length; i++) {
            String formatted = logger.formatMessage(
                i + 1,
                "2025-07-05 14:24:26",
                24 + i * 5,
                "statement",
                "",
                sqls[i],
                url
            );
            
            stdoutLogger.logText(formatted);
        }
        
        System.out.println();
        
        // 清理环境变量
        System.clearProperty("spring.profiles.active");
    }
}
