/*
 * Copyright (c) 2011-2025, b<PERSON><PERSON><PERSON><PERSON> (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.tuowan.yeliao.commons.config.p6spy;

import com.easyooo.framework.common.util.StringUtils;
import com.p6spy.engine.spy.appender.MessageFormattingStrategy;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * P6spy SQL 打印策略
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
public class P6SpyLogger implements MessageFormattingStrategy {

    private static String environmentName;

    // URL到数据库名称的映射缓存
    private static final Map<String, String> urlToDatabaseNameCache = new ConcurrentHashMap<>();

    static {
        // 尝试从系统属性或环境变量获取环境信息
        environmentName = getEnvironmentName();
    }

    /**
     * 注册URL到数据库名称的映射
     * 这个方法可以被JdbcDataSource调用来注册正确的映射关系
     */
    public static void registerDatabaseMapping(String url, String databaseName) {
        if (StringUtils.isNotBlank(url) && StringUtils.isNotBlank(databaseName)) {
            urlToDatabaseNameCache.put(url, databaseName);
        }
    }

    /**
     * 获取环境名称
     */
    private static String getEnvironmentName() {
        // 优先从spring.profiles.active获取
        String profiles = System.getProperty("spring.profiles.active");
        if (profiles == null || profiles.trim().isEmpty()) {
            profiles = System.getenv("SPRING_PROFILES_ACTIVE");
        }

        if (profiles != null && !profiles.trim().isEmpty()) {
            String profile = profiles.split(",")[0].trim().toUpperCase();
            switch (profile) {
                case "DEV":
                case "DEVELOPMENT":
                    return "DEV";
                case "TEST":
                case "TESTING":
                    return "TEST";
                case "PROD":
                case "PRODUCTION":
                    return "PROD";
                default:
                    return "DEV";
            }
        }

        // 默认返回DEV
        return "DEV";
    }

    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category,
                                String prepared, String sql, String url) {
        if (StringUtils.isBlank(sql)) {
            return "";
        }

        // 清理SQL语句，去除多余空格和换行
        String cleanSql = sql.replaceAll("[\\s]+", " ").trim();

        // 获取环境名称
        String env = environmentName != null ? environmentName : "DEV";

        // 从URL中提取数据库名称
        String databaseName = extractDatabaseName(url, connectionId);

        // 格式化日志：[SQL-DEV] 2025-07-04 10:30:15 | 执行时间: 25ms | 数据库: YL_CONFIG | SQL: SELECT * FROM t_user WHERE user_id = 12345
        return String.format("[SQL-%s] %s | 执行时间: %dms | 数据库: %s | SQL: %s",
                env, now, elapsed, databaseName, cleanSql);
    }

    /**
     * 从JDBC URL中提取数据库名称
     */
    private String extractDatabaseName(String url, int connectionId) {
        if (StringUtils.isBlank(url)) {
            return "UNKNOWN";
        }

        // 先检查缓存
        String cachedName = urlToDatabaseNameCache.get(url);
        if (cachedName != null) {
            return cachedName;
        }

        try {
            // 处理p6spy包装的URL：*************************************************
            String actualUrl = url;
            if (url.startsWith("jdbc:p6spy:")) {
                actualUrl = url.substring("jdbc:p6spy:".length());
                if (!actualUrl.startsWith("jdbc:")) {
                    actualUrl = "jdbc:" + actualUrl;
                }
            }

            // 从URL中提取数据库名称
            // 支持格式：******************************************************************************
            if (actualUrl.contains("/") && actualUrl.contains("://")) {
                String[] parts = actualUrl.split("://");
                if (parts.length > 1) {
                    String hostAndDb = parts[1];
                    // 找到最后一个/后面的数据库名
                    int lastSlash = hostAndDb.lastIndexOf('/');
                    if (lastSlash >= 0 && lastSlash < hostAndDb.length() - 1) {
                        String dbPart = hostAndDb.substring(lastSlash + 1);

                        // 去掉URL参数（? 或 & 开始的部分）
                        int questionMark = dbPart.indexOf('?');
                        if (questionMark >= 0) {
                            dbPart = dbPart.substring(0, questionMark);
                        }
                        int ampersand = dbPart.indexOf('&');
                        if (ampersand >= 0) {
                            dbPart = dbPart.substring(0, ampersand);
                        }

                        // 映射物理数据库名到逻辑数据库名
                        String logicalDbName = mapToLogicalDatabaseName(dbPart.trim());

                        // 缓存结果
                        urlToDatabaseNameCache.put(url, logicalDbName);

                        return logicalDbName;
                    }
                }
            }
        } catch (Exception e) {
            // 如果解析失败，返回连接ID
            String fallbackName = "CONN-" + connectionId;
            urlToDatabaseNameCache.put(url, fallbackName);
            return fallbackName;
        }

        String fallbackName = "CONN-" + connectionId;
        urlToDatabaseNameCache.put(url, fallbackName);
        return fallbackName;
    }

    /**
     * 将物理数据库名映射到逻辑数据库名
     */
    private String mapToLogicalDatabaseName(String physicalDbName) {
        if (StringUtils.isBlank(physicalDbName)) {
            return "UNKNOWN";
        }

        String dbName = physicalDbName.toLowerCase();

        // 根据物理数据库名映射到逻辑数据库名
        // 优先匹配已知的逻辑数据库名
        if (dbName.startsWith("yl_")) {
            return physicalDbName.toUpperCase();
        }

        // 根据物理数据库名的关键字进行映射
        if (dbName.contains("config") || dbName.equals("shanghai")) {
            return "YL_CONFIG";
        } else if (dbName.contains("user")) {
            return "YL_USER";
        } else if (dbName.contains("busi") || dbName.contains("business")) {
            return "YL_BUSI";
        } else if (dbName.contains("acct") || dbName.contains("account")) {
            return "YL_ACCT";
        } else if (dbName.contains("social")) {
            return "YL_SOCIAL";
        } else if (dbName.contains("query")) {
            return "YL_QUERY";
        } else if (dbName.contains("cms")) {
            return "YL_CMS";
        } else if (dbName.contains("log")) {
            return "YL_LOG";
        } else if (dbName.contains("rep") && dbName.contains("query")) {
            return "YL_REP_QUERY";
        } else if (dbName.contains("rep")) {
            return "YL_REP";
        } else if (dbName.contains("cash")) {
            return "YL_CASH";
        } else {
            // 如果无法映射，返回原始名称的大写形式
            return physicalDbName.toUpperCase();
        }
    }
}