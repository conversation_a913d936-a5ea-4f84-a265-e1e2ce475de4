package com.tuowan.yeliao.commons.config.p6spy;

import com.easyooo.framework.common.util.StringUtils;
import com.p6spy.engine.spy.appender.MessageFormattingStrategy;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * P6spy SQL 打印策略
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
public class P6SpyLogger implements MessageFormattingStrategy {

    private static String environmentName;

    // URL到数据库名称的映射缓存
    private static final Map<String, String> urlToDatabaseNameCache = new ConcurrentHashMap<>();

    // 物理数据库名到逻辑数据库名的映射缓存
    private static final Map<String, String> physicalToLogicalCache = new ConcurrentHashMap<>();

    static {
        // 尝试从系统属性或环境变量获取环境信息
        environmentName = getEnvironmentName();

        // 初始化数据库名称映射
        initializeDatabaseMapping();
    }

    /**
     * 初始化数据库名称映射
     * 基于DBType枚举自动构建映射关系
     */
    private static void initializeDatabaseMapping() {
        try {
            // 使用反射获取DBType枚举的所有值
            Class<?> dbTypeClass = Class.forName("com.tuowan.yeliao.commons.config.enums.DBType");
            Object[] dbTypes = (Object[]) dbTypeClass.getMethod("values").invoke(null);

            for (Object dbType : dbTypes) {
                String id = (String) dbTypeClass.getMethod("getId").invoke(dbType);
                String schema = (String) dbTypeClass.getMethod("getSchema").invoke(dbType);

                // 注册映射关系：id -> schema
                physicalToLogicalCache.put(id.toLowerCase(), schema);

                // 注册一些常见的变体
                if ("config".equals(id)) {
                    physicalToLogicalCache.put("shanghai", schema); // 特殊映射
                }
            }
        } catch (Exception e) {
            // 如果反射失败，使用硬编码的备用映射
            initializeFallbackMapping();
        }
    }

    /**
     * 备用的硬编码映射（当反射失败时使用）
     */
    private static void initializeFallbackMapping() {
        physicalToLogicalCache.put("config", "YL_CONFIG");
        physicalToLogicalCache.put("shanghai", "YL_CONFIG");
        physicalToLogicalCache.put("user", "YL_USER");
        physicalToLogicalCache.put("busi", "YL_BUSI");
        physicalToLogicalCache.put("business", "YL_BUSI");
        physicalToLogicalCache.put("acct", "YL_ACCT");
        physicalToLogicalCache.put("account", "YL_ACCT");
        physicalToLogicalCache.put("social", "YL_SOCIAL");
        physicalToLogicalCache.put("query", "YL_QUERY");
        physicalToLogicalCache.put("cms", "YL_CMS");
        physicalToLogicalCache.put("log", "YL_LOG");
        physicalToLogicalCache.put("rep", "YL_REP");
        physicalToLogicalCache.put("repquery", "YL_REP_QUERY");
        physicalToLogicalCache.put("cash", "YL_CASH");
    }

    /**
     * 注册URL到数据库名称的映射
     * 这个方法可以被JdbcDataSource调用来注册正确的映射关系
     */
    public static void registerDatabaseMapping(String url, String databaseName) {
        if (StringUtils.isNotBlank(url) && StringUtils.isNotBlank(databaseName)) {
            urlToDatabaseNameCache.put(url, databaseName);
        }
    }

    /**
     * 注册物理数据库名到逻辑数据库名的映射
     * 允许外部代码动态添加映射关系
     */
    public static void registerPhysicalToLogicalMapping(String physicalName, String logicalName) {
        if (StringUtils.isNotBlank(physicalName) && StringUtils.isNotBlank(logicalName)) {
            physicalToLogicalCache.put(physicalName.toLowerCase(), logicalName);
        }
    }

    /**
     * 获取环境名称
     */
    private static String getEnvironmentName() {
        // 优先从spring.profiles.active获取
        String profiles = System.getProperty("spring.profiles.active");
        if (profiles == null || profiles.trim().isEmpty()) {
            profiles = System.getenv("SPRING_PROFILES_ACTIVE");
        }

        if (profiles != null && !profiles.trim().isEmpty()) {
            String profile = profiles.split(",")[0].trim().toUpperCase();
            switch (profile) {
                case "DEV":
                case "DEVELOPMENT":
                    return "DEV";
                case "TEST":
                case "TESTING":
                    return "TEST";
                case "PROD":
                case "PRODUCTION":
                    return "PROD";
                default:
                    return "DEV";
            }
        }

        // 默认返回DEV
        return "DEV";
    }

    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category,
                                String prepared, String sql, String url) {
        if (StringUtils.isBlank(sql)) {
            return "";
        }

        // 清理SQL语句，去除多余空格和换行
        String cleanSql = sql.replaceAll("[\\s]+", " ").trim();

        // 获取环境名称
        String env = environmentName != null ? environmentName : "DEV";

        // 从URL中提取数据库名称
        String databaseName = extractDatabaseName(url, connectionId);

        // 获取调用栈信息
        String callerInfo = getCallerInfo();

        // 格式化日志：[SQL-DEV] 2025-07-04 10:30:15 | 执行时间: 25ms | 数据库: YL_CONFIG | 调用: UserMapper.selectById:45 | SQL: SELECT * FROM t_user WHERE user_id = 12345
        return String.format("[SQL-%s] %s | 执行时间: %dms | 数据库: %s | 调用: %s | SQL: %s",
                env, now, elapsed, databaseName, callerInfo, cleanSql);
    }

    /**
     * 从JDBC URL中提取数据库名称
     */
    private String extractDatabaseName(String url, int connectionId) {
        if (StringUtils.isBlank(url)) {
            return "UNKNOWN";
        }

        // 先检查缓存
        String cachedName = urlToDatabaseNameCache.get(url);
        if (cachedName != null) {
            return cachedName;
        }

        try {
            // 处理p6spy包装的URL：*************************************************
            String actualUrl = url;
            if (url.startsWith("jdbc:p6spy:")) {
                actualUrl = url.substring("jdbc:p6spy:".length());
                if (!actualUrl.startsWith("jdbc:")) {
                    actualUrl = "jdbc:" + actualUrl;
                }
            }

            // 从URL中提取数据库名称
            // 支持格式：******************************************************************************
            if (actualUrl.contains("/") && actualUrl.contains("://")) {
                String[] parts = actualUrl.split("://");
                if (parts.length > 1) {
                    String hostAndDb = parts[1];
                    // 找到最后一个/后面的数据库名
                    int lastSlash = hostAndDb.lastIndexOf('/');
                    if (lastSlash >= 0 && lastSlash < hostAndDb.length() - 1) {
                        String dbPart = hostAndDb.substring(lastSlash + 1);

                        // 去掉URL参数（? 或 & 开始的部分）
                        int questionMark = dbPart.indexOf('?');
                        if (questionMark >= 0) {
                            dbPart = dbPart.substring(0, questionMark);
                        }
                        int ampersand = dbPart.indexOf('&');
                        if (ampersand >= 0) {
                            dbPart = dbPart.substring(0, ampersand);
                        }

                        // 映射物理数据库名到逻辑数据库名
                        String logicalDbName = mapToLogicalDatabaseName(dbPart.trim());

                        // 缓存结果
                        urlToDatabaseNameCache.put(url, logicalDbName);

                        return logicalDbName;
                    }
                }
            }
        } catch (Exception e) {
            // 如果解析失败，返回连接ID
            String fallbackName = "CONN-" + connectionId;
            urlToDatabaseNameCache.put(url, fallbackName);
            return fallbackName;
        }

        String fallbackName = "CONN-" + connectionId;
        urlToDatabaseNameCache.put(url, fallbackName);
        return fallbackName;
    }

    /**
     * 将物理数据库名映射到逻辑数据库名
     */
    private String mapToLogicalDatabaseName(String physicalDbName) {
        if (StringUtils.isBlank(physicalDbName)) {
            return "UNKNOWN";
        }

        String dbName = physicalDbName.toLowerCase();

        // 优先匹配已知的逻辑数据库名（以YL_开头）
        if (dbName.startsWith("yl_")) {
            return physicalDbName.toUpperCase();
        }

        // 精确匹配
        String exactMatch = physicalToLogicalCache.get(dbName);
        if (exactMatch != null) {
            return exactMatch;
        }

        // 模糊匹配（包含关键字）
        for (Map.Entry<String, String> entry : physicalToLogicalCache.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            // 跳过特殊的精确匹配项（如shanghai）
            if ("shanghai".equals(key)) {
                continue;
            }

            if (dbName.contains(key)) {
                return value;
            }
        }

        // 如果无法映射，返回原始名称的大写形式
        return physicalDbName.toUpperCase();
    }

    /**
     * 获取调用SQL的Java类、方法和行号信息
     */
    private String getCallerInfo() {
        try {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();

            // 遍历调用栈，找到第一个非框架代码的调用点
            for (StackTraceElement element : stackTrace) {
                String className = element.getClassName();
                String methodName = element.getMethodName();
                int lineNumber = element.getLineNumber();

                // 跳过框架相关的类
                if (isFrameworkClass(className)) {
                    continue;
                }

                // 找到业务代码，返回简化的类名.方法名:行号
                String simpleClassName = getSimpleClassName(className);
                return String.format("%s.%s:%d", simpleClassName, methodName, lineNumber);
            }
        } catch (Exception e) {
            // 如果获取失败，返回默认值
            return "Unknown";
        }

        return "Unknown";
    }

    /**
     * 判断是否为框架类（需要跳过的类）
     */
    private boolean isFrameworkClass(String className) {
        if (className == null) {
            return true;
        }

        // 跳过的框架类包名
        String[] frameworkPackages = {
            "java.lang",
            "java.util",
            "java.sql",
            "javax.sql",
            "com.p6spy",
            "com.zaxxer.hikari",
            "org.apache.ibatis",
            "org.mybatis",
            "org.springframework.jdbc",
            "com.mysql.cj",
            "com.mysql.jdbc",
            "sun.reflect",
            "jdk.internal",
            "org.springframework.aop",
            "org.springframework.cglib",
            "net.sf.cglib",
            "com.tuowan.yeliao.commons.config.p6spy"  // 跳过p6spy相关类
        };

        for (String pkg : frameworkPackages) {
            if (className.startsWith(pkg)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取简化的类名（去掉包名，只保留类名）
     */
    private String getSimpleClassName(String fullClassName) {
        if (fullClassName == null) {
            return "Unknown";
        }

        int lastDot = fullClassName.lastIndexOf('.');
        if (lastDot >= 0 && lastDot < fullClassName.length() - 1) {
            return fullClassName.substring(lastDot + 1);
        }

        return fullClassName;
    }
}