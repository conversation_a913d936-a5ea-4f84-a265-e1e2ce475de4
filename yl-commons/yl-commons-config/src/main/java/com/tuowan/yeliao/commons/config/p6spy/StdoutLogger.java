/*
 * Copyright (c) 2011-2025, b<PERSON><PERSON><PERSON><PERSON> (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.tuowan.yeliao.commons.config.p6spy;

import java.util.regex.Pattern;

/**
 * 输出 SQL 日志
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
public class StdoutLogger extends com.p6spy.engine.spy.appender.StdoutLogger {

    // ANSI颜色代码
    private static final String RESET = "\u001B[0m";
    private static final String BRIGHT_GREEN = "\u001B[92m";  // 亮绿色 - SQL标签
    private static final String BRIGHT_CYAN = "\u001B[96m";   // 亮青色 - 时间
    private static final String BRIGHT_YELLOW = "\u001B[93m"; // 亮黄色 - 执行时间
    private static final String BRIGHT_BLUE = "\u001B[94m";   // 亮蓝色 - 数据库信息和SQL关键字
    private static final String BRIGHT_RED = "\u001B[91m";    // 亮红色 - SQL语句
    private static final String BLUE = "\u001B[34m";          // 蓝色 - SQL关键字

    // SQL关键字正则表达式（不区分大小写）
    private static final Pattern SQL_KEYWORDS = Pattern.compile(
        "\\b(SELECT|FROM|WHERE|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER|TABLE|INDEX|" +
        "JOIN|LEFT|RIGHT|INNER|OUTER|ON|AS|AND|OR|NOT|IN|EXISTS|LIKE|BETWEEN|" +
        "ORDER|BY|GROUP|HAVING|LIMIT|OFFSET|UNION|DISTINCT|COUNT|SUM|AVG|MAX|MIN|" +
        "INTO|VALUES|SET|CASE|WHEN|THEN|ELSE|END|IF|NULL|IS|ASC|DESC)\\b",
        Pattern.CASE_INSENSITIVE
    );

    @Override
    public void logText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return;
        }

        String coloredText = addColors(text);
        System.out.println(coloredText);
    }

    /**
     * 为日志文本添加颜色
     */
    private String addColors(String text) {
        // 解析日志格式：[SQL-DEV] 2025-07-04 10:30:15 | 执行时间: 25ms | 数据库: YL_CONFIG | SQL: SELECT * FROM t_user WHERE user_id = 12345

        if (!text.startsWith("[SQL-")) {
            return text;
        }

        StringBuilder result = new StringBuilder();

        // 分割日志各部分
        String[] parts = text.split(" \\| ");

        for (int i = 0; i < parts.length; i++) {
            if (i > 0) {
                result.append(" | ");
            }

            String part = parts[i];

            if (i == 0) {
                // [SQL-DEV] 时间部分
                if (part.contains("] ")) {
                    String[] tagAndTime = part.split("] ", 2);
                    result.append(BRIGHT_GREEN).append(tagAndTime[0]).append("]").append(RESET)
                          .append(" ")
                          .append(BRIGHT_CYAN).append(tagAndTime[1]).append(RESET);
                } else {
                    result.append(BRIGHT_GREEN).append(part).append(RESET);
                }
            } else if (part.startsWith("执行时间:")) {
                // 执行时间部分
                result.append(BRIGHT_YELLOW).append(part).append(RESET);
            } else if (part.startsWith("数据库:")) {
                // 数据库部分
                result.append(BRIGHT_BLUE).append(part).append(RESET);
            } else if (part.startsWith("SQL:")) {
                // SQL部分 - 高亮关键字
                String sqlPart = part.substring(4).trim(); // 去掉"SQL:"前缀
                String highlightedSql = highlightSqlKeywords(sqlPart);
                result.append(BRIGHT_BLUE).append("SQL:").append(RESET)
                      .append(" ")
                      .append(highlightedSql);
            } else {
                result.append(part);
            }
        }

        return result.toString();
    }

    /**
     * 高亮SQL关键字
     */
    private String highlightSqlKeywords(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        // 使用正则表达式替换SQL关键字，添加颜色
        // SQL关键字使用蓝色，普通SQL内容使用红色
        String highlighted = SQL_KEYWORDS.matcher(sql).replaceAll(
            BLUE + "$0" + RESET + BRIGHT_RED
        );

        // 为整个SQL添加基础红色
        return BRIGHT_RED + highlighted + RESET;
    }
}